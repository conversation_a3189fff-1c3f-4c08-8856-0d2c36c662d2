{"name": "store-rating-app", "version": "1.0.0", "description": "Store rating application with role-based authentication", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "postgresql", "jwt", "rating"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "sequelize": "^6.35.0", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^3.0.2"}}